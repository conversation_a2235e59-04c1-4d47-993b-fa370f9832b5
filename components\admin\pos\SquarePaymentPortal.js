import { useEffect, useRef } from 'react'
import { createPortal } from 'react-dom'

const SquarePaymentPortal = ({
  containerElement,
  onFormReady,
  onFormError,
  isProduction,
  appId,
  locationId
}) => {
  const squareFormRef = useRef(null)
  const mountedRef = useRef(true)
  const initializationRef = useRef(false)

  useEffect(() => {
    console.log('🔄 SquarePaymentPortal initializing...')

    const initializeSquareForm = async () => {
      // Prevent multiple initializations
      if (initializationRef.current) {
        console.log('⚠️ Square form initialization already in progress')
        return
      }

      if (!window.Square || !containerElement || !appId || !locationId) {
        console.error('❌ Missing Square dependencies:', {
          square: !!window.Square,
          container: !!containerElement,
          containerInDOM: containerElement && document.contains(containerElement),
          appId: !!appId,
          locationId: !!locationId
        })
        onFormError(new Error('Missing Square SDK or configuration'))
        return
      }

      // Verify container is in DOM and visible
      if (!document.contains(containerElement)) {
        console.error('❌ Container element not in DOM')
        onFormError(new Error('Container element not found in DOM'))
        return
      }

      initializationRef.current = true

      try {
        console.log('🔄 Initializing Square form in isolated portal...')

        // Clear container safely
        if (containerElement.innerHTML) {
          containerElement.innerHTML = ''
        }

        // Initialize Square payments
        const payments = window.Square.payments(appId, locationId)

        // Configure card options - FIXED: Removed invalid 'padding' property
        const cardOptions = {
          style: {
            '.input-container': {
              borderColor: '#e0e0e0',
              borderRadius: '8px'
              // REMOVED: padding: '12px' - This is invalid for Square SDK
            },
            '.input-container.is-focus': {
              borderColor: '#4ECDC4'
            },
            '.input-container.is-error': {
              borderColor: '#dc3545'
            },
            '.message-text': {
              color: '#dc3545'
            }
          }
        }

        // Create and attach card form with enhanced error handling
        const card = await payments.card(cardOptions)

        // Verify container still exists before attaching
        if (!document.contains(containerElement)) {
          throw new Error('Container element removed from DOM during initialization')
        }

        // Attach with retry mechanism for DOM conflicts
        let attachAttempts = 0
        const maxAttachAttempts = 3

        while (attachAttempts < maxAttachAttempts) {
          try {
            await card.attach(containerElement)
            break
          } catch (attachError) {
            attachAttempts++
            console.warn(`⚠️ Square form attach attempt ${attachAttempts} failed:`, attachError.message)

            if (attachAttempts >= maxAttachAttempts) {
              throw new Error(`Failed to attach Square form after ${maxAttachAttempts} attempts: ${attachError.message}`)
            }

            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 100 * attachAttempts))
          }
        }

        squareFormRef.current = card

        // Ensure container is visible after attachment
        if (containerElement.style) {
          containerElement.style.visibility = 'visible'
          containerElement.style.opacity = '1'
        }

        console.log('✅ Square form initialized successfully in isolated portal')
        onFormReady(card)

      } catch (error) {
        console.error('❌ Square form initialization failed:', error)
        onFormError(error)
      } finally {
        initializationRef.current = false
      }
    }

    // Initialize form with delay to ensure DOM is ready
    const initTimeout = setTimeout(initializeSquareForm, 100)

    // Cleanup function
    return () => {
      console.log('🧹 Cleaning up Square form in portal...')
      mountedRef.current = false
      initializationRef.current = false

      clearTimeout(initTimeout)

      if (squareFormRef.current) {
        try {
          squareFormRef.current.destroy()
          console.log('✅ Square form cleaned up successfully')
        } catch (error) {
          console.warn('⚠️ Error cleaning up Square form:', error)
        }
        squareFormRef.current = null
      }
    }
  }, [containerElement, appId, locationId, onFormReady, onFormError])

  // This component doesn't render anything directly
  // The Square form is rendered into the containerElement via isolated portal
  return null
}

export default SquarePaymentPortal
