import React from 'react'

/**
 * Error Boundary specifically designed to handle Square SDK DOM manipulation conflicts
 * and React Error #130 issues during payment processing
 */
class SquareErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null,
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    console.error('🚨 SquareErrorBoundary caught error:', error)
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    console.error('🚨 Square SDK Error Boundary Details:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      isReactError130: error.message.includes('removeChild') || 
                      error.message.includes('Node') ||
                      error.message.includes('DOM'),
      timestamp: new Date().toISOString()
    })

    this.setState({
      error,
      errorInfo,
      hasError: true
    })

    // Report error to parent component if callback provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Auto-recovery for DOM manipulation errors
    if (error.message.includes('removeChild') || 
        error.message.includes('Node') ||
        error.message.includes('DOM')) {
      console.log('🔄 Attempting auto-recovery from DOM manipulation error...')
      
      setTimeout(() => {
        if (this.state.retryCount < 3) {
          this.handleRetry()
        }
      }, 1000)
    }
  }

  handleRetry = () => {
    console.log(`🔄 Square Error Boundary retry attempt ${this.state.retryCount + 1}/3`)
    
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }))

    // Notify parent to reinitialize Square form
    if (this.props.onRetry) {
      this.props.onRetry()
    }
  }

  handleReset = () => {
    console.log('🔄 Square Error Boundary manual reset')
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    })

    // Notify parent to completely reset Square integration
    if (this.props.onReset) {
      this.props.onReset()
    }
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI for Square payment errors
      return (
        <div style={{
          padding: '20px',
          border: '2px solid #dc3545',
          borderRadius: '8px',
          backgroundColor: '#f8f9fa',
          textAlign: 'center',
          margin: '10px 0'
        }}>
          <div style={{ color: '#dc3545', fontSize: '24px', marginBottom: '10px' }}>
            ⚠️
          </div>
          <h4 style={{ color: '#dc3545', margin: '0 0 10px 0' }}>
            Payment Form Error
          </h4>
          <p style={{ margin: '0 0 15px 0', color: '#6c757d' }}>
            {this.state.error?.message.includes('removeChild') || 
             this.state.error?.message.includes('Node') || 
             this.state.error?.message.includes('DOM')
              ? 'A DOM conflict occurred with the payment form. This is usually temporary.'
              : 'An error occurred while loading the payment form.'}
          </p>
          
          <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
            {this.state.retryCount < 3 && (
              <button
                onClick={this.handleRetry}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Retry ({3 - this.state.retryCount} attempts left)
              </button>
            )}
            
            <button
              onClick={this.handleReset}
              style={{
                padding: '8px 16px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Reset Payment Form
            </button>
          </div>

          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details style={{ marginTop: '15px', textAlign: 'left' }}>
              <summary style={{ cursor: 'pointer', color: '#6c757d' }}>
                Error Details (Development)
              </summary>
              <pre style={{ 
                fontSize: '12px', 
                backgroundColor: '#f1f3f4', 
                padding: '10px', 
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {this.state.error.stack}
              </pre>
            </details>
          )}
        </div>
      )
    }

    return this.props.children
  }
}

export default SquareErrorBoundary
