// Console error monitoring for debugging
(function() {
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  const originalConsoleLog = console.log;

  // Store errors for debugging
  window.consoleErrors = [];
  window.consoleWarnings = [];
  window.consoleLogs = [];

  console.error = function(...args) {
    window.consoleErrors.push({
      timestamp: new Date().toISOString(),
      message: args.join(' '),
      stack: new Error().stack
    });
    originalConsoleError.apply(console, args);
  };

  console.warn = function(...args) {
    window.consoleWarnings.push({
      timestamp: new Date().toISOString(),
      message: args.join(' ')
    });
    originalConsoleWarn.apply(console, args);
  };

  console.log = function(...args) {
    window.consoleLogs.push({
      timestamp: new Date().toISOString(),
      message: args.join(' ')
    });
    originalConsoleLog.apply(console, args);
  };

  // Global error handler
  window.addEventListener('error', function(event) {
    window.consoleErrors.push({
      timestamp: new Date().toISOString(),
      message: `Global Error: ${event.message}`,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    });
  });

  // Unhandled promise rejection handler
  window.addEventListener('unhandledrejection', function(event) {
    window.consoleErrors.push({
      timestamp: new Date().toISOString(),
      message: `Unhandled Promise Rejection: ${event.reason}`,
      type: 'unhandledrejection'
    });
  });

  // Helper function to get all errors
  window.getConsoleErrors = function() {
    return {
      errors: window.consoleErrors,
      warnings: window.consoleWarnings,
      logs: window.consoleLogs.filter(log => 
        log.message.includes('Square') || 
        log.message.includes('Payment') || 
        log.message.includes('Error') ||
        log.message.includes('🔄') ||
        log.message.includes('✅') ||
        log.message.includes('❌')
      )
    };
  };

  console.log('Console monitoring initialized');
})();
